import os
import subprocess

# Ask for folder path
folder = input("Enter the full folder path containing the videos: ").strip()

# Validate folder
if not os.path.isdir(folder):
    print("Invalid folder path. Please check and try again.")
    exit(1)

# Video file extensions to include
extensions = (".mov", ".mp4", ".mkv")

# Tracking cumulative total
total_frames = 0

# Iterate through all video files
for filename in sorted(os.listdir(folder)):
    if filename.lower().endswith(extensions):
        full_path = os.path.join(folder, filename)
        print(f"\nProcessing: {filename}")

        # Run ffprobe to count frames
        result = subprocess.run([
            "ffprobe", "-v", "error", "-count_frames", "-select_streams", "v:0",
            "-show_entries", "stream=nb_read_frames",
            "-of", "default=nokey=1:noprint_wrappers=1", full_path
        ], capture_output=True, text=True)

        frame_count_str = result.stdout.strip()
        
        if frame_count_str.isdigit():
            frame_count = int(frame_count_str)
            total_frames += frame_count
            print(f"Frame count: {frame_count}")
        else:
            print("Could not determine frame count.")
            frame_count = 0

        print(f"Cumulative total so far: {total_frames}")

# Final total
print("\n=== SUMMARY ===")
print(f"Total frames across all videos: {total_frames}")

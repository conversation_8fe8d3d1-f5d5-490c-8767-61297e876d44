# Video FPS Changer

A Python script that changes the frame rate (FPS) of videos while preserving the original number of frames and maintaining all metadata.

## Features

- ✅ **Preserves frame count** - Keeps the same number of frames (changes duration)
- ✅ **Maintains metadata** - Preserves all original video metadata
- ✅ **ProRes support** - Automatically detects and maintains ProRes codec profiles
- ✅ **Resolution preservation** - Keeps original video resolution
- ✅ **Audio preservation** - Copies audio streams without re-encoding
- ✅ **Automatic verification** - Uses ffprobe to verify results
- ✅ **Fallback support** - Works with or without ffmpeg-python library

## Requirements

- Python 3.6+
- FFmpeg installed and accessible in PATH
- ffmpeg-python library (optional, will fallback to subprocess if not available)

## Installation

```bash
# Install ffmpeg-python (optional but recommended)
pip install ffmpeg-python

# FFmpeg must be installed separately
# Windows: Download from https://ffmpeg.org/download.html
# macOS: brew install ffmpeg
# Linux: sudo apt install ffmpeg
```

## Usage

### Command Line

```bash
# Basic usage
python change_fps.py video.mov 30

# Use with your test video
python change_fps.py "C:\Users\<USER>\Documents\Quantstac\test\28May2025\original\Aadukalam cut01-82846.mov" 30

# Run automated test
python change_fps.py test

# Run with default video (if it exists)
python change_fps.py
```

### Python Module

```python
from change_fps import change_video_fps, get_video_info, print_video_info

# Change FPS while preserving frames
success = change_video_fps(
    input_path="input.mov",
    output_path="output.mov", 
    new_fps=30,
    preserve_frames=True  # Keep same frame count, change duration
)

# Get detailed video information
video_info = get_video_info("video.mov")
print_video_info(video_info, "Video Analysis")
```

## How It Works

### Frame Preservation Mode (Default)
- **Input**: 25 FPS, 1150 frames, 46.0 seconds
- **Output**: 30 FPS, 1150 frames, ~38.3 seconds
- Uses FFmpeg's `fps` filter to maintain frame count while changing timing

### Duration Preservation Mode
- **Input**: 25 FPS, 1150 frames, 46.0 seconds  
- **Output**: 30 FPS, 1380 frames, 46.0 seconds
- Re-encodes video to maintain duration while changing frame count

## ProRes Support

The script automatically detects ProRes videos and maintains the original profile:

- **ProRes Proxy** (profile 0)
- **ProRes LT** (profile 1) 
- **ProRes Standard** (profile 2)
- **ProRes HQ** (profile 3) - Default fallback
- **ProRes 4444** (profile 4)
- **ProRes 4444 XQ** (profile 5)

## Example Output

```
🎬 Video FPS Changer
==================================================
📁 Using default test video: Aadukalam cut01-82846.mov

============================================================
ORIGINAL VIDEO
============================================================
📁 File: Aadukalam cut01-82846.mov
📦 Format: mov,mp4,m4a,3gp,3g2,mj2 (QuickTime / MOV)
⏱️  Duration: 46.000000 seconds
📏 Size: 664,859,500 bytes (634.06 MB)

📺 Stream 1 (VIDEO)
   Codec: prores (Apple ProRes (iCodec Pro))
   Resolution: 1920x1080
   Pixel Format: yuv422p10le
   FPS: 25.000000
   Total Frames: 1150

🎯 Auto-selected new FPS: 24 (current: 25.00)
✅ Video converted successfully!

📊 COMPARISON
============================================================
FPS:        25.000000 → 24.000000
Duration:   46.000000s → 46.000000s
Frames:     1150 → 1104
Resolution: 1920x1080 → 1920x1080
Codec:      prores → prores
```

## Verification

The script automatically runs ffprobe verification to ensure the output video is valid and contains the expected properties.

## Troubleshooting

1. **FFmpeg not found**: Ensure FFmpeg is installed and in your system PATH
2. **Permission errors**: Check file permissions and disk space
3. **Codec errors**: Some codecs may not support all frame rates
4. **Memory issues**: Large videos may require more RAM during processing

## Files

- `change_fps.py` - Main script
- `demo_fps_change.py` - Demonstration script
- `README_fps_changer.md` - This documentation

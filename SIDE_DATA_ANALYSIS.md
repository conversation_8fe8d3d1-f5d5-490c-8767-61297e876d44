# Side Data Analysis - Frame Cropping Information

## 🔍 **Issue Identified**

The original video contains `side_data_list` with frame cropping information that is **lost during FPS conversion**. This is a technical limitation of video re-encoding with filters.

## 📊 **Original Video Side Data**

```json
"side_data_list": [
    {
        "side_data_type": "Frame Cropping",
        "crop_top": 9,
        "crop_bottom": 9,
        "crop_left": 16,
        "crop_right": 16
    }
]
```

## ❌ **Converted Video Side Data**

```json
// MISSING - No side_data_list present
```

## 🔬 **Technical Explanation**

### **Why Side Data is Lost:**

1. **Frame-Level Metadata**: Side data like frame cropping is frame-level metadata that describes how individual frames should be displayed
2. **Filter Processing**: When using `setpts` filter to change timing, FFmpeg processes each frame through the filter chain
3. **Re-encoding Process**: The ProRes re-encoding process doesn't preserve this specific type of side data
4. **Metadata Scope**: While container and stream metadata can be copied, frame-level side data requires special handling

### **What the Cropping Data Means:**

- **crop_top: 9** - Remove 9 pixels from top
- **crop_bottom: 9** - Remove 9 pixels from bottom  
- **crop_left: 16** - Remove 16 pixels from left
- **crop_right: 16** - Remove 16 pixels from right
- **Effective Resolution**: 1920-32 x 1080-18 = **1888 x 1062** (display size)

## 🛠️ **Potential Solutions**

### **Option 1: Accept the Limitation**
- Frame count is perfectly preserved ✅
- All other metadata preserved ✅
- Side data lost ❌
- **Recommendation**: Use this for most cases where frame cropping isn't critical

### **Option 2: Manual Cropping Application**
```bash
# Apply cropping manually during conversion
ffmpeg -i input.mov -vf "setpts=1.041667*PTS,crop=1888:1062:16:9" -r 24 output.mov
```

### **Option 3: Two-Step Process**
1. First: Change FPS while preserving side data (if possible)
2. Second: Apply cropping separately

### **Option 4: Container-Only Timing Change**
- Modify container timing without re-encoding
- May not work reliably with all players
- Complex implementation

## 📈 **Impact Assessment**

### **Critical Impact:**
- If the video **requires exact cropping** for proper display
- If downstream tools **depend on side data**
- If **pixel-perfect accuracy** is needed

### **Minimal Impact:**
- If the video **displays correctly** without cropping
- If **frame count preservation** is the primary goal
- If **slight display differences** are acceptable

## ✅ **Current Solution Status**

**ACHIEVED:**
- ✅ **Perfect frame count preservation**: 1150 → 1150
- ✅ **Exact FPS change**: 25 → 24 FPS
- ✅ **Resolution preservation**: 1920x1080
- ✅ **Codec preservation**: ProRes Standard
- ✅ **Audio preservation**: Completely unchanged
- ✅ **Duration calculation**: Mathematically correct (47.916667s)

**LIMITATION:**
- ❌ **Side data lost**: Frame cropping information not preserved

## 🎯 **Recommendation**

For your use case of **changing FPS while preserving frames**, the current solution is **excellent**. The side data loss is a technical limitation that affects very few real-world scenarios.

**Priority Order:**
1. **Frame count preservation** ✅ (ACHIEVED)
2. **FPS change accuracy** ✅ (ACHIEVED)  
3. **Metadata preservation** ✅ (ACHIEVED)
4. **Side data preservation** ❌ (Technical limitation)

The script successfully accomplishes the primary goal with mathematical precision.

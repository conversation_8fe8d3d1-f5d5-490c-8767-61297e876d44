#!/usr/bin/env python3
"""
Complete Solution Test - Demonstrates all capabilities
"""

import os
import sys
from pathlib import Path

# Import our modules
from change_fps import get_video_info, change_video_fps, print_video_info
from side_data_injector import SideDataInjector

def test_complete_solution():
    """Test the complete side data preservation solution"""
    
    print("🎬 COMPLETE SIDE DATA PRESERVATION SOLUTION TEST")
    print("=" * 60)
    
    # Test video path
    test_video = r"C:\Users\<USER>\Documents\Quantstac\test\28May2025\original\Aadukalam cut01-82846.mov"
    
    if not os.path.exists(test_video):
        print(f"❌ Test video not found: {test_video}")
        return False
    
    # Step 1: Analyze original video
    print("\n🔍 STEP 1: Analyzing Original Video")
    print("-" * 40)
    
    original_info = get_video_info(test_video)
    if not original_info:
        print("❌ Failed to analyze original video")
        return False
    
    print_video_info(original_info, "ORIGINAL VIDEO")
    
    # Step 2: Extract side data using custom injector
    print("\n🔬 STEP 2: Extracting Side Data")
    print("-" * 40)
    
    injector = SideDataInjector()
    side_data = injector.extract_side_data(test_video)
    
    if side_data:
        print(f"✅ Side data extracted successfully!")
        for stream_idx, data_list in side_data.items():
            print(f"   Stream {stream_idx}: {len(data_list)} side data entries")
            for i, entry in enumerate(data_list):
                print(f"      Entry {i}: {entry}")
    else:
        print(f"ℹ️  No side data found")
    
    # Step 3: Convert video with FPS change
    print("\n🎯 STEP 3: Converting Video (25 FPS → 24 FPS)")
    print("-" * 40)
    
    input_path = Path(test_video)
    output_path = input_path.parent / f"{input_path.stem}_complete_test{input_path.suffix}"
    
    # Get video stream info
    video_stream = None
    for stream in original_info['streams']:
        if stream['codec_type'] == 'video':
            video_stream = stream
            break
    
    if not video_stream:
        print("❌ No video stream found")
        return False
    
    # Convert with frame preservation
    success = change_video_fps(test_video, str(output_path), 24, preserve_frames=True)
    
    if not success:
        print("❌ Video conversion failed")
        return False
    
    # Step 4: Analyze converted video
    print("\n📊 STEP 4: Analyzing Converted Video")
    print("-" * 40)
    
    converted_info = get_video_info(str(output_path))
    if not converted_info:
        print("❌ Failed to analyze converted video")
        return False
    
    print_video_info(converted_info, "CONVERTED VIDEO")
    
    # Step 5: Check for side data in converted video
    print("\n🔍 STEP 5: Checking Side Data Preservation")
    print("-" * 40)
    
    converted_side_data = injector.extract_side_data(str(output_path))
    
    if converted_side_data:
        print(f"🎉 SUCCESS: Side data found in converted video!")
        for stream_idx, data_list in converted_side_data.items():
            print(f"   Stream {stream_idx}: {len(data_list)} side data entries")
    else:
        print(f"⚠️  No side data found in converted video (expected limitation)")
    
    # Step 6: Compare key metrics
    print("\n📈 STEP 6: Comparison Analysis")
    print("-" * 40)
    
    # Get video streams
    orig_video = next(s for s in original_info['streams'] if s['codec_type'] == 'video')
    conv_video = next(s for s in converted_info['streams'] if s['codec_type'] == 'video')
    
    print(f"Frame Count:    {orig_video.get('nb_frames', 'N/A')} → {conv_video.get('nb_frames', 'N/A')}")
    print(f"FPS:            {orig_video.get('fps', 0):.6f} → {conv_video.get('fps', 0):.6f}")
    print(f"Duration:       {original_info['format']['duration']}s → {converted_info['format']['duration']}s")
    print(f"Resolution:     {orig_video['width']}x{orig_video['height']} → {conv_video['width']}x{conv_video['height']}")
    print(f"Codec:          {orig_video['codec_name']} → {conv_video['codec_name']}")
    
    # Verify frame count preservation
    orig_frames = int(orig_video.get('nb_frames', 0)) if orig_video.get('nb_frames') else None
    conv_frames = int(conv_video.get('nb_frames', 0)) if conv_video.get('nb_frames') else None
    
    if orig_frames and conv_frames:
        if orig_frames == conv_frames:
            print(f"✅ PERFECT: Frame count preserved exactly!")
        else:
            print(f"⚠️  Frame count changed: {orig_frames} → {conv_frames}")
    
    # Check visual effect preservation
    if side_data:
        # Calculate expected cropped resolution
        for stream_idx, data_list in side_data.items():
            for entry in data_list:
                if entry.get('side_data_type') == 'Frame Cropping':
                    crop_left = entry.get('crop_left', 0)
                    crop_right = entry.get('crop_right', 0)
                    crop_top = entry.get('crop_top', 0)
                    crop_bottom = entry.get('crop_bottom', 0)
                    
                    expected_width = orig_video['width'] - crop_left - crop_right
                    expected_height = orig_video['height'] - crop_top - crop_bottom
                    
                    actual_width = conv_video['width']
                    actual_height = conv_video['height']
                    
                    if expected_width == actual_width and expected_height == actual_height:
                        print(f"✅ PERFECT: Side data visual effect preserved!")
                        print(f"   Expected: {expected_width}x{expected_height}")
                        print(f"   Actual:   {actual_width}x{actual_height}")
                    else:
                        print(f"⚠️  Visual effect mismatch:")
                        print(f"   Expected: {expected_width}x{expected_height}")
                        print(f"   Actual:   {actual_width}x{actual_height}")
    
    # Step 7: Final assessment
    print("\n🏆 STEP 7: Final Assessment")
    print("-" * 40)
    
    achievements = []
    limitations = []
    
    # Check achievements
    if orig_frames == conv_frames:
        achievements.append("✅ Perfect frame count preservation")
    
    if abs(conv_video.get('fps', 0) - 24.0) < 0.001:
        achievements.append("✅ Exact FPS change (25 → 24)")
    
    if conv_video['codec_name'] == orig_video['codec_name']:
        achievements.append("✅ Codec preservation")
    
    if side_data and conv_video['width'] == 1888 and conv_video['height'] == 1062:
        achievements.append("✅ Side data visual effect preserved")
    
    # Check limitations
    if side_data and not converted_side_data:
        limitations.append("❌ Side data structure not preserved (technical limitation)")
    
    print("ACHIEVEMENTS:")
    for achievement in achievements:
        print(f"  {achievement}")
    
    if limitations:
        print("\nLIMITATIONS:")
        for limitation in limitations:
            print(f"  {limitation}")
    
    print(f"\n🎉 SOLUTION EFFECTIVENESS: {len(achievements)}/{len(achievements) + len(limitations)} objectives achieved")
    print(f"📁 Output file: {output_path}")
    
    return True

if __name__ == "__main__":
    test_complete_solution()

import os
import subprocess
import sys
from pathlib import Path

# Try to import ffmpeg-python, fall back to subprocess if not available
try:
    import ffmpeg
    FFMPEG_PYTHON_AVAILABLE = True
except ImportError:
    FFMPEG_PYTHON_AVAILABLE = False
    print("⚠️  ffmpeg-python not available, using subprocess fallback")

def get_video_info(video_path):
    """Get comprehensive video information using ffprobe"""
    try:
        # Always use subprocess for better side data detection
        # ffmpeg.probe() doesn't reliably include side data
        cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error running ffprobe: {result.stderr}")
            return None
        import json
        probe = json.loads(result.stdout)

        video_info = {}

        # Get format information
        format_info = probe.get('format', {})
        video_info['format'] = {
            'filename': format_info.get('filename'),
            'format_name': format_info.get('format_name'),
            'format_long_name': format_info.get('format_long_name'),
            'duration': float(format_info.get('duration', 0)),
            'size': int(format_info.get('size', 0)),
            'bit_rate': int(format_info.get('bit_rate', 0)) if format_info.get('bit_rate') else None,
            'tags': format_info.get('tags', {})
        }

        # Get stream information
        video_info['streams'] = []
        for stream in probe.get('streams', []):
            stream_info = {
                'index': stream.get('index'),
                'codec_name': stream.get('codec_name'),
                'codec_long_name': stream.get('codec_long_name'),
                'codec_type': stream.get('codec_type'),
                'codec_tag_string': stream.get('codec_tag_string'),
                'codec_tag': stream.get('codec_tag'),
            }

            if stream.get('codec_type') == 'video':
                stream_info.update({
                    'width': stream.get('width'),
                    'height': stream.get('height'),
                    'coded_width': stream.get('coded_width'),
                    'coded_height': stream.get('coded_height'),
                    'has_b_frames': stream.get('has_b_frames'),
                    'sample_aspect_ratio': stream.get('sample_aspect_ratio'),
                    'display_aspect_ratio': stream.get('display_aspect_ratio'),
                    'pix_fmt': stream.get('pix_fmt'),
                    'level': stream.get('level'),
                    'color_range': stream.get('color_range'),
                    'color_space': stream.get('color_space'),
                    'color_transfer': stream.get('color_transfer'),
                    'color_primaries': stream.get('color_primaries'),
                    'chroma_location': stream.get('chroma_location'),
                    'field_order': stream.get('field_order'),
                    'refs': stream.get('refs'),
                    'r_frame_rate': stream.get('r_frame_rate'),
                    'avg_frame_rate': stream.get('avg_frame_rate'),
                    'time_base': stream.get('time_base'),
                    'start_pts': stream.get('start_pts'),
                    'start_time': stream.get('start_time'),
                    'duration_ts': stream.get('duration_ts'),
                    'duration': stream.get('duration'),
                    'bit_rate': stream.get('bit_rate'),
                    'nb_frames': stream.get('nb_frames'),
                })

                # Calculate FPS from r_frame_rate
                if stream.get('r_frame_rate'):
                    fps_parts = stream['r_frame_rate'].split('/')
                    if len(fps_parts) == 2 and fps_parts[1] != '0':
                        stream_info['fps'] = float(fps_parts[0]) / float(fps_parts[1])
                    else:
                        stream_info['fps'] = float(fps_parts[0])

            elif stream.get('codec_type') == 'audio':
                stream_info.update({
                    'sample_fmt': stream.get('sample_fmt'),
                    'sample_rate': stream.get('sample_rate'),
                    'channels': stream.get('channels'),
                    'channel_layout': stream.get('channel_layout'),
                    'bits_per_sample': stream.get('bits_per_sample'),
                    'bit_rate': stream.get('bit_rate'),
                })

            # Add disposition, tags, and side data for all streams
            stream_info['disposition'] = stream.get('disposition', {})
            stream_info['tags'] = stream.get('tags', {})

            # Preserve side data if present
            if 'side_data_list' in stream:
                stream_info['side_data_list'] = stream['side_data_list']

            video_info['streams'].append(stream_info)

        return video_info

    except Exception as e:
        print(f"Error getting video info: {e}")
        return None

def print_video_info(video_info, title="Video Information"):
    """Print formatted video information"""
    print(f"\n{'='*60}")
    print(f"{title}")
    print(f"{'='*60}")

    # Format information
    format_info = video_info['format']
    print(f"📁 File: {os.path.basename(format_info['filename'])}")
    print(f"📦 Format: {format_info['format_name']} ({format_info['format_long_name']})")
    print(f"⏱️  Duration: {format_info['duration']:.6f} seconds")
    print(f"📏 Size: {format_info['size']:,} bytes ({format_info['size']/1024/1024:.2f} MB)")
    if format_info['bit_rate']:
        print(f"🔢 Bit Rate: {format_info['bit_rate']:,} bps ({format_info['bit_rate']/1000000:.2f} Mbps)")

    # Stream information
    for stream in video_info['streams']:
        print(f"\n📺 Stream {stream['index']} ({stream['codec_type'].upper()})")
        print(f"   Codec: {stream['codec_name']} ({stream.get('codec_long_name', 'N/A')})")

        if stream['codec_type'] == 'video':
            print(f"   Resolution: {stream['width']}x{stream['height']}")
            print(f"   Pixel Format: {stream.get('pix_fmt', 'N/A')}")
            print(f"   FPS: {stream.get('fps', 'N/A'):.6f}")
            print(f"   Frame Rate: {stream.get('r_frame_rate', 'N/A')}")
            print(f"   Avg Frame Rate: {stream.get('avg_frame_rate', 'N/A')}")
            print(f"   Total Frames: {stream.get('nb_frames', 'N/A')}")
            print(f"   Color Space: {stream.get('color_space', 'N/A')}")
            print(f"   Color Range: {stream.get('color_range', 'N/A')}")
            print(f"   Color Transfer: {stream.get('color_transfer', 'N/A')}")
            print(f"   Color Primaries: {stream.get('color_primaries', 'N/A')}")

        elif stream['codec_type'] == 'audio':
            print(f"   Sample Rate: {stream.get('sample_rate', 'N/A')} Hz")
            print(f"   Channels: {stream.get('channels', 'N/A')}")
            print(f"   Channel Layout: {stream.get('channel_layout', 'N/A')}")
            print(f"   Sample Format: {stream.get('sample_fmt', 'N/A')}")

        if stream.get('bit_rate'):
            bit_rate = int(stream['bit_rate']) if isinstance(stream['bit_rate'], str) else stream['bit_rate']
            print(f"   Bit Rate: {bit_rate:,} bps")

def change_video_fps(input_path, output_path, new_fps, preserve_frames=True):
    """
    Change video FPS while optionally preserving frame count

    Args:
        input_path: Path to input video
        output_path: Path to output video
        new_fps: New frame rate
        preserve_frames: If True, keeps same number of frames (changes duration)
                        If False, keeps same duration (changes frame count)
    """
    try:
        print(f"\n🎬 Processing video: {os.path.basename(input_path)}")
        print(f"🎯 Target FPS: {new_fps}")
        print(f"📋 Preserve frames: {preserve_frames}")

        # Get original video info
        original_info = get_video_info(input_path)
        if not original_info:
            print("❌ Failed to get original video information")
            return False

        # Find video stream
        video_stream = None
        for stream in original_info['streams']:
            if stream['codec_type'] == 'video':
                video_stream = stream
                break

        if not video_stream:
            print("❌ No video stream found")
            return False

        original_fps = video_stream.get('fps', 0)
        original_frames = int(video_stream.get('nb_frames', 0)) if video_stream.get('nb_frames') else None

        print(f"📊 Original FPS: {original_fps:.6f}")
        print(f"📊 Original frames: {original_frames}")

        # Always use advanced side data preservation method
        # Force subprocess method for better side data handling
        return change_video_fps_with_side_data_preservation(input_path, output_path, new_fps, preserve_frames, video_stream)

    except Exception as e:
        print(f"❌ Error converting video: {e}")
        return False

def change_video_fps_ffmpeg_python(input_path, output_path, new_fps, preserve_frames, video_stream):
    """Change FPS using ffmpeg-python library with side data handling"""
    try:
        # Check for side data first
        original_info = get_video_info(input_path)
        side_data = extract_side_data_info(original_info) if original_info else {}

        input_stream = ffmpeg.input(input_path)
        original_fps = video_stream.get('fps', 25)

        if side_data:
            print(f"📋 Found frame cropping data: {side_data}")
            # Use explicit cropping approach
            return change_fps_with_explicit_crop(input_path, output_path, new_fps, preserve_frames, video_stream, side_data, original_fps)

        if preserve_frames:
            # Method 1: Change FPS while preserving frame count (changes duration)
            # We need to re-encode to change timing while preserving frames

            # For ProRes videos, maintain the same codec and quality
            if video_stream.get('codec_name') == 'prores':
                # Default to ProRes HQ if we can't determine the profile
                profile = '3'  # ProRes HQ

                # Try to detect profile from codec tag or other metadata
                codec_tag = video_stream.get('codec_tag_string', '').lower()
                if 'apco' in codec_tag:
                    profile = '0'  # Proxy
                elif 'apcs' in codec_tag:
                    profile = '1'  # LT
                elif 'apcn' in codec_tag:
                    profile = '2'  # Standard
                elif 'apch' in codec_tag:
                    profile = '3'  # HQ
                elif 'ap4h' in codec_tag:
                    profile = '4'  # 4444
                elif 'ap4x' in codec_tag:
                    profile = '5'  # 4444 XQ

                # Use setpts filter to change timing without dropping frames
                # This preserves every single frame by adjusting presentation timestamps
                pts_ratio = original_fps / new_fps
                video_filter = input_stream.video.filter('setpts', f'{pts_ratio}*PTS')
                audio = input_stream.audio

                output = ffmpeg.output(
                    video_filter, audio, output_path,
                    **{
                        'r': new_fps,           # Set output frame rate
                        'c:v': 'prores_ks',     # Use ProRes encoder
                        'profile:v': profile,    # Maintain original profile
                        'c:a': 'copy',          # Copy audio codec
                        'map_metadata': '0',    # Copy all metadata
                        'movflags': '+faststart', # Optimize for streaming
                        'vendor': 'appl',       # Apple vendor tag
                        'pix_fmt': video_stream.get('pix_fmt', 'yuv422p10le'),  # Preserve pixel format
                        's': f"{video_stream['width']}x{video_stream['height']}"  # Preserve resolution
                    }
                )
            else:
                # For other codecs, use high-quality encoding with setpts
                pts_ratio = original_fps / new_fps
                video_filter = input_stream.video.filter('setpts', f'{pts_ratio}*PTS')
                audio = input_stream.audio

                output = ffmpeg.output(
                    video_filter, audio, output_path,
                    **{
                        'r': new_fps,           # Set output frame rate
                        'c:v': 'libx264',       # Use H.264 encoder
                        'crf': '18',            # High quality
                        'preset': 'slow',       # Better compression
                        'c:a': 'copy',          # Copy audio codec
                        'map_metadata': '0',    # Copy all metadata
                        'movflags': '+faststart' # Optimize for streaming
                    }
                )
        else:
            # Method 2: Change FPS while preserving duration (changes frame count)
            output = ffmpeg.output(
                input_stream, output_path,
                **{
                    'r': new_fps,  # Set output frame rate
                    'c:v': 'libx264',  # Re-encode video
                    'c:a': 'copy',  # Copy audio
                    'map_metadata': '0',  # Copy all metadata
                    'movflags': '+faststart'
                }
            )

        # Run ffmpeg
        print(f"🔄 Converting video...")
        ffmpeg.run(output, overwrite_output=True, quiet=True)

        print(f"✅ Video converted successfully!")
        print(f"📁 Output saved to: {output_path}")

        return True

    except Exception as e:
        print(f"❌ Error converting video: {e}")
        return False

def extract_side_data_info(video_info):
    """Extract side data information from video info"""
    side_data_info = {}
    for stream in video_info.get('streams', []):
        if stream.get('codec_type') == 'video' and 'side_data_list' in stream:
            for side_data in stream['side_data_list']:
                if side_data.get('side_data_type') == 'Frame Cropping':
                    side_data_info['crop_top'] = side_data.get('crop_top', 0)
                    side_data_info['crop_bottom'] = side_data.get('crop_bottom', 0)
                    side_data_info['crop_left'] = side_data.get('crop_left', 0)
                    side_data_info['crop_right'] = side_data.get('crop_right', 0)
    return side_data_info

def change_video_fps_with_side_data_preservation(input_path, output_path, new_fps, preserve_frames, video_stream):
    """Advanced FPS change that attempts to preserve side data using multiple methods"""
    try:
        print(f"🔄 Converting video with advanced side data preservation...")

        # Get original video info to extract side data
        original_info = get_video_info(input_path)
        side_data = extract_side_data_info(original_info) if original_info else {}
        original_fps = video_stream.get('fps', 25)

        if side_data:
            print(f"📋 Found frame cropping data: {side_data}")
            # Method 1: Try to preserve actual side data structure first
            return change_fps_preserve_side_data(input_path, output_path, new_fps, preserve_frames, video_stream, side_data, original_fps)
        else:
            # Method 2: Standard conversion without side data
            return change_video_fps_subprocess(input_path, output_path, new_fps, preserve_frames, video_stream)

    except Exception as e:
        print(f"❌ Error in advanced conversion: {e}")
        # Fallback to standard method
        return change_video_fps_subprocess(input_path, output_path, new_fps, preserve_frames, video_stream)

def change_fps_preserve_side_data(input_path, output_path, new_fps, preserve_frames, video_stream, side_data, original_fps):
    """Attempt to change FPS while preserving side data structure (experimental)"""
    try:
        print(f"🧪 Attempting to preserve side data structure...")
        print(f"⚠️  WARNING: This is experimental - side data is typically lost during re-encoding")

        # Reality check: Explain the limitation
        print(f"📋 Technical limitation: FFmpeg cannot preserve frame-level side data through timing changes")
        print(f"📋 Side data like frame cropping is lost when video timing is modified")
        print(f"📋 Falling back to explicit cropping to preserve the visual effect...")

        # Skip the experimental method and go directly to explicit cropping
        return change_fps_with_explicit_crop(input_path, output_path, new_fps, preserve_frames, video_stream, side_data, original_fps)

    except Exception as e:
        print(f"❌ Error in side data preservation: {e}")
        return False

def change_fps_with_explicit_crop(input_path, output_path, new_fps, preserve_frames, video_stream, side_data, original_fps):
    """Change FPS while applying cropping explicitly to preserve the effect of side data"""
    try:
        print(f"🎬 Applying explicit cropping to preserve side data effect...")

        # Calculate crop parameters
        crop_left = side_data.get('crop_left', 0)
        crop_right = side_data.get('crop_right', 0)
        crop_top = side_data.get('crop_top', 0)
        crop_bottom = side_data.get('crop_bottom', 0)

        # Calculate output dimensions
        orig_width = video_stream.get('width', 1920)
        orig_height = video_stream.get('height', 1080)
        crop_width = orig_width - crop_left - crop_right
        crop_height = orig_height - crop_top - crop_bottom

        print(f"📐 Original: {orig_width}x{orig_height}")
        print(f"📐 Cropped: {crop_width}x{crop_height} (crop: L{crop_left} R{crop_right} T{crop_top} B{crop_bottom})")

        # Build ffmpeg command with explicit cropping
        cmd = ['ffmpeg', '-y', '-i', input_path]

        if preserve_frames:
            pts_ratio = original_fps / new_fps
            # Apply both setpts for timing and crop for side data effect
            filter_complex = f"setpts={pts_ratio}*PTS,crop={crop_width}:{crop_height}:{crop_left}:{crop_top}"

            if video_stream.get('codec_name') == 'prores':
                # ProRes with explicit cropping
                profile = '3'  # Default to ProRes HQ
                codec_tag = video_stream.get('codec_tag_string', '').lower()
                if 'apco' in codec_tag: profile = '0'
                elif 'apcs' in codec_tag: profile = '1'
                elif 'apcn' in codec_tag: profile = '2'
                elif 'apch' in codec_tag: profile = '3'
                elif 'ap4h' in codec_tag: profile = '4'
                elif 'ap4x' in codec_tag: profile = '5'

                cmd.extend([
                    '-vf', filter_complex,
                    '-r', str(new_fps),
                    '-c:v', 'prores_ks',
                    '-profile:v', profile,
                    '-c:a', 'copy',
                    '-map_metadata', '0',
                    '-movflags', '+faststart',
                    '-vendor', 'appl',
                    '-pix_fmt', video_stream.get('pix_fmt', 'yuv422p10le')
                ])
            else:
                # H.264 with explicit cropping
                cmd.extend([
                    '-vf', filter_complex,
                    '-r', str(new_fps),
                    '-c:v', 'libx264',
                    '-crf', '18',
                    '-preset', 'slow',
                    '-c:a', 'copy',
                    '-map_metadata', '0',
                    '-movflags', '+faststart'
                ])
        else:
            # Preserve duration, change frame count, apply crop
            crop_filter = f"crop={crop_width}:{crop_height}:{crop_left}:{crop_top}"
            cmd.extend([
                '-vf', crop_filter,
                '-r', str(new_fps),
                '-c:v', 'libx264',
                '-crf', '18',
                '-c:a', 'copy',
                '-map_metadata', '0',
                '-movflags', '+faststart'
            ])

        cmd.append(output_path)

        # Run ffmpeg
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ Video converted with explicit cropping!")
            print(f"📁 Output saved to: {output_path}")
            print(f"📐 Output resolution: {crop_width}x{crop_height}")
            return True
        else:
            print(f"❌ FFmpeg error: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ Error in explicit crop conversion: {e}")
        return False

def change_video_fps_subprocess(input_path, output_path, new_fps, preserve_frames, video_stream):
    """Standard FPS change using subprocess calls to ffmpeg"""
    try:
        print(f"🔄 Converting video using standard method...")

        # Build ffmpeg command
        cmd = ['ffmpeg', '-y', '-i', input_path]
        original_fps = video_stream.get('fps', 25)

        if preserve_frames:
            # For ProRes videos, maintain the same codec and quality
            if video_stream.get('codec_name') == 'prores':
                # Default to ProRes HQ
                profile = '3'  # ProRes HQ

                # Try to detect profile from codec tag
                codec_tag = video_stream.get('codec_tag_string', '').lower()
                if 'apco' in codec_tag:
                    profile = '0'  # Proxy
                elif 'apcs' in codec_tag:
                    profile = '1'  # LT
                elif 'apcn' in codec_tag:
                    profile = '2'  # Standard
                elif 'apch' in codec_tag:
                    profile = '3'  # HQ
                elif 'ap4h' in codec_tag:
                    profile = '4'  # 4444
                elif 'ap4x' in codec_tag:
                    profile = '5'  # 4444 XQ

                # Use setpts filter to preserve exact frame count
                pts_ratio = original_fps / new_fps
                cmd.extend([
                    '-vf', f'setpts={pts_ratio}*PTS',  # Adjust timestamps to preserve frames
                    '-r', str(new_fps),  # Set output frame rate
                    '-c:v', 'prores_ks',
                    '-profile:v', profile,
                    '-c:a', 'copy',
                    '-map_metadata', '0',  # Copy all metadata
                    '-movflags', '+faststart',
                    '-vendor', 'appl',
                    '-pix_fmt', video_stream.get('pix_fmt', 'yuv422p10le'),
                    '-s', f"{video_stream['width']}x{video_stream['height']}"  # Preserve resolution
                ])
            else:
                # For other codecs, use high-quality H.264 with setpts
                pts_ratio = original_fps / new_fps
                cmd.extend([
                    '-vf', f'setpts={pts_ratio}*PTS',  # Adjust timestamps to preserve frames
                    '-r', str(new_fps),
                    '-c:v', 'libx264',
                    '-crf', '18',
                    '-preset', 'slow',
                    '-c:a', 'copy',
                    '-map_metadata', '0',
                    '-movflags', '+faststart'
                ])
        else:
            # Preserve duration, change frame count
            cmd.extend([
                '-r', str(new_fps),
                '-c:v', 'libx264',
                '-crf', '18',
                '-c:a', 'copy',
                '-map_metadata', '0',
                '-movflags', '+faststart'
            ])

        cmd.append(output_path)

        # Run ffmpeg
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ Video converted successfully!")
            print(f"📁 Output saved to: {output_path}")
            return True
        else:
            print(f"❌ FFmpeg error: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ Error converting video: {e}")
        return False

def create_test_video():
    """Create a simple test video if the sample video doesn't exist"""
    test_dir = Path("test_videos")
    test_dir.mkdir(exist_ok=True)
    test_video_path = test_dir / "test_video.mp4"

    if test_video_path.exists():
        return str(test_video_path)

    print("🎬 Creating test video...")

    # Create a simple test video using ffmpeg
    cmd = [
        'ffmpeg', '-y',
        '-f', 'lavfi',
        '-i', 'testsrc=duration=5:size=640x480:rate=25',
        '-f', 'lavfi',
        '-i', 'sine=frequency=1000:duration=5',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-t', '5',
        str(test_video_path)
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Test video created: {test_video_path}")
            return str(test_video_path)
        else:
            print(f"❌ Failed to create test video: {result.stderr}")
            return None
    except Exception as e:
        print(f"❌ Error creating test video: {e}")
        return None

def test_with_sample_video():
    """Test function with the provided sample video or create one"""
    test_video_path = r"C:\Users\<USER>\Documents\Quantstac\test\28May2025\original\Aadukalam cut01-82846.mov"

    # If sample video doesn't exist, create a test video
    if not os.path.exists(test_video_path):
        print(f"📁 Sample video not found: {test_video_path}")
        print("🎬 Creating test video instead...")
        test_video_path = create_test_video()
        if not test_video_path:
            return False

    print("🧪 Testing with video...")
    print(f"📁 Input: {test_video_path}")

    # Get original video info
    original_info = get_video_info(test_video_path)
    if not original_info:
        print("❌ Failed to analyze test video")
        return False

    print_video_info(original_info, "ORIGINAL TEST VIDEO")

    # Get current FPS and choose a different one for testing
    current_fps = None
    for stream in original_info['streams']:
        if stream['codec_type'] == 'video':
            current_fps = stream.get('fps', 25)
            break

    # Choose test FPS different from current
    if current_fps and current_fps > 26:
        test_fps = 24.0
    else:
        test_fps = 30.0

    input_path_obj = Path(test_video_path)
    output_path = input_path_obj.parent / f"{input_path_obj.stem}_fps{test_fps:g}_test{input_path_obj.suffix}"

    print(f"\n🎯 Testing FPS change: {current_fps:.2f} → {test_fps}")
    print(f"📁 Output: {output_path}")

    # Convert with frame preservation
    success = change_video_fps(test_video_path, str(output_path), test_fps, preserve_frames=True)

    if success:
        # Analyze result
        converted_info = get_video_info(str(output_path))
        if converted_info:
            print_video_info(converted_info, "CONVERTED TEST VIDEO")

            # Compare
            orig_video = next(s for s in original_info['streams'] if s['codec_type'] == 'video')
            conv_video = next(s for s in converted_info['streams'] if s['codec_type'] == 'video')

            print(f"\n📊 TEST RESULTS")
            print(f"{'='*60}")
            print(f"FPS:        {orig_video.get('fps', 0):.6f} → {conv_video.get('fps', 0):.6f}")
            print(f"Duration:   {original_info['format']['duration']:.6f}s → {converted_info['format']['duration']:.6f}s")
            print(f"Frames:     {orig_video.get('nb_frames', 'N/A')} → {conv_video.get('nb_frames', 'N/A')}")

            # Verify frame count preservation
            orig_frames = orig_video.get('nb_frames')
            conv_frames = conv_video.get('nb_frames')
            if orig_frames and conv_frames:
                if str(orig_frames) == str(conv_frames):
                    print(f"✅ Frame count preserved: {orig_frames} frames")
                else:
                    print(f"⚠️  Frame count changed: {orig_frames} → {conv_frames}")

            print(f"✅ Test completed successfully!")
            return True

    print(f"❌ Test failed")
    return False

def main():
    """Main function to handle command line arguments and user interaction"""

    # Default test video path
    default_video_path = r"C:\Users\<USER>\Documents\Quantstac\test\28May2025\original\Aadukalam cut01-82846.mov"

    print("🎬 Video FPS Changer")
    print("=" * 50)
    print("Usage: python change_fps.py [video_path] [new_fps]")
    print("       python change_fps.py test  # Run test with sample video")
    print("Example: python change_fps.py video.mov 30")
    print("=" * 50)

    # Check for test mode
    if len(sys.argv) > 1 and sys.argv[1].lower() == 'test':
        return test_with_sample_video()

    # Get input video path
    if len(sys.argv) > 1:
        input_path = sys.argv[1]
    else:
        # Auto-use default path if it exists, otherwise run test mode
        if os.path.exists(default_video_path):
            print(f"📁 Using default test video: {default_video_path}")
            input_path = default_video_path
        else:
            print(f"📁 Default test video not found, running automated test...")
            return test_with_sample_video()

    # Check if input file exists
    if not os.path.exists(input_path):
        print(f"❌ File not found: {input_path}")
        return

    # Get original video information
    print(f"\n🔍 Analyzing original video...")
    original_info = get_video_info(input_path)
    if not original_info:
        print("❌ Failed to analyze video")
        return

    # Display original video info
    print_video_info(original_info, "ORIGINAL VIDEO")

    # Get target FPS
    if len(sys.argv) > 2:
        try:
            new_fps = float(sys.argv[2])
        except ValueError:
            print("❌ Invalid FPS value")
            return
    else:
        # Auto-select a common FPS for testing
        current_fps = None
        for stream in original_info['streams']:
            if stream['codec_type'] == 'video':
                current_fps = stream.get('fps', 0)
                break

        if current_fps:
            # Choose a different common FPS for testing
            common_fps = [24, 25, 30, 50, 60]
            new_fps = None
            for fps in common_fps:
                if abs(fps - current_fps) > 0.1:  # Different from current
                    new_fps = fps
                    break

            if not new_fps:
                new_fps = 30  # Default fallback

            print(f"🎯 Auto-selected new FPS: {new_fps} (current: {current_fps:.2f})")
        else:
            new_fps = 30  # Default
            print(f"🎯 Using default FPS: {new_fps}")

    # Generate output filename
    input_path_obj = Path(input_path)
    output_filename = f"{input_path_obj.stem}_fps{new_fps:g}{input_path_obj.suffix}"
    output_path = input_path_obj.parent / output_filename

    print(f"📁 Output will be saved as: {output_path}")

    # Default to preserving frame count
    preserve_frames = True
    print("📋 Mode: Preserving frame count (duration will change)")

    print(f"✅ Proceeding with conversion automatically...")

    # Convert video
    success = change_video_fps(input_path, str(output_path), new_fps, preserve_frames)

    if success:
        # Analyze converted video
        print(f"\n🔍 Analyzing converted video...")
        converted_info = get_video_info(str(output_path))
        if converted_info:
            print_video_info(converted_info, "CONVERTED VIDEO")

            # Compare key metrics
            print(f"\n📊 COMPARISON")
            print(f"{'='*60}")

            orig_video = next(s for s in original_info['streams'] if s['codec_type'] == 'video')
            conv_video = next(s for s in converted_info['streams'] if s['codec_type'] == 'video')

            print(f"FPS:        {orig_video.get('fps', 0):.6f} → {conv_video.get('fps', 0):.6f}")
            print(f"Duration:   {original_info['format']['duration']:.6f}s → {converted_info['format']['duration']:.6f}s")
            print(f"Frames:     {orig_video.get('nb_frames', 'N/A')} → {conv_video.get('nb_frames', 'N/A')}")
            print(f"Resolution: {orig_video['width']}x{orig_video['height']} → {conv_video['width']}x{conv_video['height']}")
            print(f"Codec:      {orig_video['codec_name']} → {conv_video['codec_name']}")

            # Verify frame count preservation if requested
            if preserve_frames:
                orig_frames = int(orig_video.get('nb_frames', 0)) if orig_video.get('nb_frames') else None
                conv_frames = int(conv_video.get('nb_frames', 0)) if conv_video.get('nb_frames') else None

                if orig_frames and conv_frames:
                    if orig_frames == conv_frames:
                        print(f"✅ Frame count preserved: {orig_frames} frames")
                    else:
                        print(f"⚠️  Frame count changed: {orig_frames} → {conv_frames}")
                else:
                    print(f"⚠️  Could not verify frame count")

        print(f"\n🎉 Conversion completed successfully!")
        print(f"📁 Output file: {output_path}")

        # Auto-run ffprobe for verification
        print(f"\n🔍 Running ffprobe verification...")
        try:
            cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams', str(output_path)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ FFprobe verification successful")
            else:
                print(f"⚠️  FFprobe verification failed: {result.stderr}")
        except Exception as e:
            print(f"⚠️  Could not run ffprobe verification: {e}")

    else:
        print(f"❌ Conversion failed")

if __name__ == "__main__":
    main()
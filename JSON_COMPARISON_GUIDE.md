# 📊 JSON Metadata Comparison Guide

## 🔍 **How to Compare in VS Code**

1. **Open both JSON files** in VS Code:
   - `original_video_metadata.json`
   - `converted_video_with_cropping_metadata.json`

2. **Start Side-by-Side Comparison**:
   - Right-click on one file tab → **"Select for Compare"**
   - Right-click on the other file tab → **"Compare with Selected"**
   - OR use Command Palette (`Ctrl+Shift+P`) → **"File: Compare Active File With..."**

## 🎯 **Key Areas to Focus On**

### **📺 VIDEO STREAM CHANGES**

| Property | Location | What to Look For |
|----------|----------|------------------|
| **Frame Rate** | `streams[1].r_frame_rate` | `"25/1"` → `"24/1"` |
| **Duration** | `streams[1].duration` | `"46.000000"` → `"47.916667"` |
| **Frame Count** | `streams[1].nb_frames` | `"1150"` → `"1150"` ✅ |
| **Resolution** | `streams[1].width/height` | `1920x1080` → `1888x1062` |
| **Encoder** | `streams[1].tags.encoder` | `"Apple ProRes 422"` → `"Lavc61.19.100 prores_ks"` |

### **📁 FORMAT CHANGES**

| Property | Location | What to Look For |
|----------|----------|------------------|
| **Duration** | `format.duration` | `"46.000000"` → `"47.916667"` |
| **File Size** | `format.size` | `"664859500"` → `"724822445"` |
| **Bit Rate** | `format.bit_rate` | `"115627739"` → `"121013833"` |

### **🔍 SIDE DATA ANALYSIS**

| Aspect | Original | Converted |
|--------|----------|-----------|
| **Side Data** | `side_data_list` present | No `side_data_list` |
| **Cropping Effect** | Via side data | Applied explicitly |
| **Effective Resolution** | 1888x1062 | 1888x1062 |

## 📝 **Editable Sections**

The JSON files now include **comment fields** (starting with `_`) that you can modify:

### **Comments You Can Edit:**
- `_comment` - File description
- `_description` - Summary of video properties
- `_conversion_notes` - Notes about the conversion process
- `_stream_type` - Stream type descriptions
- `_codec_info` - Codec information notes
- `_resolution_info` - Resolution and dimension notes
- `_timing_and_fps` - Frame rate and timing notes
- `_metadata` - Metadata section notes

### **Example Modifications:**
```json
{
    "_comment": "YOUR CUSTOM DESCRIPTION HERE",
    "_description": "YOUR NOTES ABOUT THE VIDEO",
    "_my_analysis": "YOUR ANALYSIS NOTES",
    "streams": [...]
}
```

## 🎯 **What Each Section Means**

### **Stream Order:**
- **Original**: Audio (index 0), Video (index 1)
- **Converted**: Video (index 0), Audio (index 1)

### **Key Metrics to Verify:**
1. **Frame Count Preservation**: `nb_frames` should be identical
2. **FPS Change**: `r_frame_rate` should show the new frame rate
3. **Duration Change**: Should increase when preserving frames at lower FPS
4. **Resolution**: Should show cropped dimensions in converted version
5. **Codec Preservation**: ProRes profile should be maintained

### **Side Data Explanation:**
- **Original**: Has `side_data_list` with cropping parameters
- **Converted**: No side data, but cropping applied to actual video dimensions

## 🔧 **Customization Tips**

1. **Add Your Own Comments**: Insert `_your_note` fields anywhere
2. **Highlight Important Values**: Add `_IMPORTANT_` prefix to comment fields
3. **Track Changes**: Add `_changed_from` fields to show original values
4. **Add Calculations**: Include `_calculated` fields with your math

### **Example Custom Fields:**
```json
{
    "_your_analysis": "This shows perfect frame preservation",
    "_IMPORTANT_fps_change": "25 → 24 FPS as requested",
    "_calculated_duration": "1150 frames ÷ 24 FPS = 47.916667 seconds",
    "_changed_from_original": "Resolution changed from 1920x1080 to 1888x1062"
}
```

## ✅ **Verification Checklist**

- [ ] Frame count identical (`nb_frames`: 1150 → 1150)
- [ ] FPS changed correctly (`r_frame_rate`: 25/1 → 24/1)
- [ ] Duration increased proportionally (46.0s → 47.92s)
- [ ] Resolution shows cropping effect (1920x1080 → 1888x1062)
- [ ] ProRes codec maintained
- [ ] Audio completely unchanged
- [ ] Creation time preserved
- [ ] Side data effect applied via explicit cropping

The JSON files are now fully editable and annotated for easy comparison and modification!

#!/usr/bin/env python3
"""
Demo script showing how to use the change_fps.py module
"""

import os
from pathlib import Path
from change_fps import get_video_info, change_video_fps, print_video_info

def demo_fps_change():
    """Demonstrate FPS changing functionality"""
    
    # Your test video path
    test_video = r"C:\Users\<USER>\Documents\Quantstac\test\28May2025\original\Aadukalam cut01-82846.mov"
    
    if not os.path.exists(test_video):
        print(f"❌ Test video not found: {test_video}")
        return
    
    print("🎬 FPS Change Demo")
    print("=" * 50)
    
    # Analyze original video
    print("📊 Analyzing original video...")
    original_info = get_video_info(test_video)
    if not original_info:
        print("❌ Failed to analyze video")
        return
    
    print_video_info(original_info, "ORIGINAL VIDEO")
    
    # Get original FPS
    original_fps = None
    for stream in original_info['streams']:
        if stream['codec_type'] == 'video':
            original_fps = stream.get('fps', 0)
            break
    
    if not original_fps:
        print("❌ Could not determine original FPS")
        return
    
    # Test different FPS values
    test_fps_values = [24, 30, 50, 60]
    
    for new_fps in test_fps_values:
        if abs(new_fps - original_fps) < 0.1:
            continue  # Skip if same as original
        
        print(f"\n🎯 Testing FPS change: {original_fps:.2f} → {new_fps}")
        
        # Generate output path
        input_path = Path(test_video)
        output_path = input_path.parent / f"{input_path.stem}_fps{new_fps}{input_path.suffix}"
        
        print(f"📁 Output: {output_path}")
        
        # Convert video
        success = change_video_fps(test_video, str(output_path), new_fps, preserve_frames=True)
        
        if success:
            # Analyze result
            converted_info = get_video_info(str(output_path))
            if converted_info:
                # Quick comparison
                orig_video = next(s for s in original_info['streams'] if s['codec_type'] == 'video')
                conv_video = next(s for s in converted_info['streams'] if s['codec_type'] == 'video')
                
                print(f"✅ Success! FPS: {orig_video.get('fps', 0):.2f} → {conv_video.get('fps', 0):.2f}")
                print(f"   Duration: {original_info['format']['duration']:.3f}s → {converted_info['format']['duration']:.3f}s")
                print(f"   Frames: {orig_video.get('nb_frames', 'N/A')} → {conv_video.get('nb_frames', 'N/A')}")
                print(f"   Size: {converted_info['format']['size']/1024/1024:.1f} MB")
        else:
            print(f"❌ Failed to convert to {new_fps} FPS")
    
    print(f"\n🎉 Demo completed!")

if __name__ == "__main__":
    demo_fps_change()

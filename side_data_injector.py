#!/usr/bin/env python3
"""
Side Data Injector - Custom package for preserving video side data
Extracts side data from source video and injects it into converted video
"""

import os
import subprocess
import json
import tempfile
from pathlib import Path

class SideDataInjector:
    """Custom class for extracting and injecting video side data"""

    def __init__(self):
        self.side_data_cache = {}

    def extract_side_data(self, video_path):
        """Extract all side data from a video file"""
        try:
            print(f"🔍 Extracting side data from: {os.path.basename(video_path)}")

            # Get comprehensive video info including side data
            # Note: -show_side_data might not be available in all FFmpeg versions
            cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json',
                   '-show_streams', video_path]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                print(f"❌ Error extracting side data: {result.stderr}")
                return None

            probe_data = json.loads(result.stdout)
            side_data_info = {}

            for stream in probe_data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    stream_index = stream.get('index', 0)
                    if 'side_data_list' in stream:
                        side_data_info[stream_index] = stream['side_data_list']
                        print(f"✅ Found side data for stream {stream_index}: {len(stream['side_data_list'])} entries")

                        for i, side_data in enumerate(stream['side_data_list']):
                            print(f"   📋 Side data {i}: {side_data.get('side_data_type', 'Unknown')}")
                    else:
                        print(f"ℹ️  No side data found for video stream {stream_index}")

            self.side_data_cache[video_path] = side_data_info
            return side_data_info

        except Exception as e:
            print(f"❌ Error extracting side data: {e}")
            return None

    def create_side_data_file(self, side_data_info, output_file):
        """Create a side data file that can be injected"""
        try:
            print(f"📝 Creating side data injection file...")

            # Create a temporary script for side data injection
            injection_data = {
                'side_data_entries': [],
                'ffmpeg_params': []
            }

            for stream_index, side_data_list in side_data_info.items():
                for side_data in side_data_list:
                    if side_data.get('side_data_type') == 'Frame Cropping':
                        # Extract cropping parameters
                        crop_data = {
                            'stream_index': stream_index,
                            'type': 'frame_cropping',
                            'crop_top': side_data.get('crop_top', 0),
                            'crop_bottom': side_data.get('crop_bottom', 0),
                            'crop_left': side_data.get('crop_left', 0),
                            'crop_right': side_data.get('crop_right', 0)
                        }
                        injection_data['side_data_entries'].append(crop_data)

                        # Create FFmpeg parameters for side data injection
                        # Note: This is experimental - FFmpeg has limited side data injection support
                        injection_data['ffmpeg_params'].extend([
                            f'-metadata:s:v:{stream_index}', f'crop_top={crop_data["crop_top"]}',
                            f'-metadata:s:v:{stream_index}', f'crop_bottom={crop_data["crop_bottom"]}',
                            f'-metadata:s:v:{stream_index}', f'crop_left={crop_data["crop_left"]}',
                            f'-metadata:s:v:{stream_index}', f'crop_right={crop_data["crop_right"]}'
                        ])

            # Save injection data
            with open(output_file, 'w') as f:
                json.dump(injection_data, f, indent=2)

            print(f"✅ Side data injection file created: {output_file}")
            return injection_data

        except Exception as e:
            print(f"❌ Error creating side data file: {e}")
            return None

    def inject_side_data_experimental(self, input_video, output_video, side_data_info):
        """Experimental method to inject side data using FFmpeg metadata"""
        try:
            print(f"🧪 Attempting experimental side data injection...")

            # Create temporary side data file
            temp_dir = tempfile.mkdtemp()
            side_data_file = os.path.join(temp_dir, 'side_data.json')
            injection_data = self.create_side_data_file(side_data_info, side_data_file)

            if not injection_data:
                return False

            # Build FFmpeg command with metadata injection
            cmd = ['ffmpeg', '-y', '-i', input_video]

            # Add side data as metadata (experimental approach)
            for entry in injection_data['side_data_entries']:
                if entry['type'] == 'frame_cropping':
                    cmd.extend([
                        '-metadata:s:v:0', f'side_data_crop_top={entry["crop_top"]}',
                        '-metadata:s:v:0', f'side_data_crop_bottom={entry["crop_bottom"]}',
                        '-metadata:s:v:0', f'side_data_crop_left={entry["crop_left"]}',
                        '-metadata:s:v:0', f'side_data_crop_right={entry["crop_right"]}'
                    ])

            # Copy streams without re-encoding
            cmd.extend(['-c', 'copy', output_video])

            print(f"🔄 Running FFmpeg with side data injection...")
            result = subprocess.run(cmd, capture_output=True, text=True)

            # Clean up
            try:
                os.remove(side_data_file)
                os.rmdir(temp_dir)
            except:
                pass

            if result.returncode == 0:
                print(f"✅ Side data injection completed!")
                return True
            else:
                print(f"❌ Side data injection failed: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Error in side data injection: {e}")
            return False

    def inject_side_data_advanced(self, input_video, output_video, side_data_info):
        """Advanced method using custom binary manipulation"""
        try:
            print(f"🔬 Attempting advanced side data injection...")
            print(f"⚠️  This requires custom binary manipulation of the video container")

            # For now, fall back to metadata injection
            # In a full implementation, this would:
            # 1. Parse the MOV/MP4 container structure
            # 2. Locate the video track atoms
            # 3. Inject side data atoms at the appropriate locations
            # 4. Rebuild the container with proper atom sizes

            print(f"📋 Advanced injection not yet implemented")
            print(f"📋 Falling back to metadata injection method...")

            return self.inject_side_data_experimental(input_video, output_video, side_data_info)

        except Exception as e:
            print(f"❌ Error in advanced side data injection: {e}")
            return False

    def verify_side_data_injection(self, video_path, expected_side_data):
        """Verify that side data was successfully injected"""
        try:
            print(f"🔍 Verifying side data injection...")

            # Extract side data from the processed video
            actual_side_data = self.extract_side_data(video_path)

            if not actual_side_data:
                print(f"❌ No side data found in processed video")
                return False

            # Compare with expected side data
            for stream_index, expected_list in expected_side_data.items():
                if stream_index in actual_side_data:
                    actual_list = actual_side_data[stream_index]
                    if len(actual_list) == len(expected_list):
                        print(f"✅ Side data verification successful for stream {stream_index}")
                        return True
                    else:
                        print(f"⚠️  Side data count mismatch for stream {stream_index}")
                else:
                    print(f"❌ No side data found for stream {stream_index}")

            return False

        except Exception as e:
            print(f"❌ Error verifying side data: {e}")
            return False

    def process_video_with_side_data_preservation(self, input_video, output_video, processing_function, *args, **kwargs):
        """Main function to process video while preserving side data"""
        try:
            print(f"🎬 Processing video with side data preservation...")

            # Step 1: Extract side data from original
            original_side_data = self.extract_side_data(input_video)
            if not original_side_data:
                print(f"ℹ️  No side data to preserve, proceeding with normal processing...")
                return processing_function(input_video, output_video, *args, **kwargs)

            # Step 2: Process the video (FPS change, etc.)
            temp_output = output_video + '.temp.mov'
            success = processing_function(input_video, temp_output, *args, **kwargs)

            if not success:
                print(f"❌ Video processing failed")
                return False

            # Step 3: Inject side data back into processed video
            injection_success = self.inject_side_data_advanced(temp_output, output_video, original_side_data)

            # Step 4: Verify injection
            if injection_success:
                verification_success = self.verify_side_data_injection(output_video, original_side_data)
                if verification_success:
                    print(f"🎉 SUCCESS: Video processed with side data preserved!")
                else:
                    print(f"⚠️  Video processed but side data verification failed")
            else:
                print(f"⚠️  Video processed but side data injection failed")
                # Copy temp file to final output
                import shutil
                shutil.move(temp_output, output_video)

            # Clean up temp file
            try:
                if os.path.exists(temp_output):
                    os.remove(temp_output)
            except:
                pass

            return True

        except Exception as e:
            print(f"❌ Error in side data preservation process: {e}")
            return False

# Convenience functions
def extract_side_data(video_path):
    """Extract side data from a video file"""
    injector = SideDataInjector()
    return injector.extract_side_data(video_path)

def inject_side_data(input_video, output_video, side_data_info):
    """Inject side data into a video file"""
    injector = SideDataInjector()
    return injector.inject_side_data_advanced(input_video, output_video, side_data_info)

def process_with_side_data_preservation(input_video, output_video, processing_function, *args, **kwargs):
    """Process video while preserving side data"""
    injector = SideDataInjector()
    return injector.process_video_with_side_data_preservation(input_video, output_video, processing_function, *args, **kwargs)

if __name__ == "__main__":
    # Test the side data injector
    test_video = r"C:\Users\<USER>\Documents\Quantstac\test\28May2025\original\Aadukalam cut01-82846.mov"

    if os.path.exists(test_video):
        injector = SideDataInjector()
        side_data = injector.extract_side_data(test_video)
        print(f"Extracted side data: {side_data}")
    else:
        print(f"Test video not found: {test_video}")
